<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" style="background: transparent; background-color: transparent;" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="2036px" height="4342px" viewBox="-0.5 -0.5 2036 4342" content="&lt;mxfile&gt;&lt;diagram name=&quot;Quy Trì<PERSON>t <PERSON>ển <PERSON>ần Mềm Tích Hợp AI&quot; id=&quot;software-dev-flow&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <g>
            <rect x="577" y="0" width="600" height="40" fill="none" stroke="none" pointer-events="all"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 598px; height: 1px; padding-top: 20px; margin-left: 578px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 24px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: normal; word-wrap: normal; ">
                                    Quy Trình Phát Triển Phần Mềm Tích Hợp AI
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="877" y="27" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="24px" text-anchor="middle" font-weight="bold">
                        Quy Trình Phát Triển Phần Mềm Tích Hợp AI
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 77 686 L 77 646 L 1651 646 L 1651 686" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 686 L 77 826 L 1651 826 L 1651 686" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 686 L 1651 686" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 666px; margin-left: 864px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    Quản Lý Dự Án (PM)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="864" y="670" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        Quản Lý Dự Án (PM)
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <ellipse cx="147" cy="736" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 736px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tiếp nhận larkbase
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="740" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tiếp nhận larkbase
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 357 736 L 430.63 736" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 435.88 736 L 428.88 739.5 L 430.63 736 L 428.88 732.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="237" y="696" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 736px; margin-left: 238px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Lên danh sách milestones, lịch demo,...
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="297" y="740" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Lên danh sách milest...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 777 736 L 782 736 Q 787 736 787 746 L 787 776 Q 787 786 777 786 L 507 786 Q 497 786 497 784.18 L 497 782.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 497 777.12 L 500.5 784.12 L 497 782.37 L 493.5 784.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 707 701 L 777 736 L 707 771 L 637 736 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 736px; margin-left: 638px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Có việc mới phát sinh
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="707" y="740" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Có việc mới phát sinh
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 557 736 L 630.63 736" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 635.88 736 L 628.88 739.5 L 630.63 736 L 628.88 732.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="437" y="696" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 736px; margin-left: 438px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo task
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="497" y="740" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tạo task
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1017 741 L 1090.63 741" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1095.88 741 L 1088.88 744.5 L 1090.63 741 L 1088.88 737.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="897" y="701" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 741px; margin-left: 898px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật trạng thái task của member
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="957" y="745" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Cập nhật trạng thái tas...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1097" y="701" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 741px; margin-left: 1098px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo task:
                                    <br/>
                                    - SA / Techlead đánh giá tài liệu cập nhật
                                    <br/>
                                    <div style="text-align: left;">
                                        <span style="background-color: transparent;">
                                            - Tester tạo tài liệu test
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1157" y="745" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tạo task:...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1307" y="701" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 741px; margin-left: 1308px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo task cho dev / designer / review code
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1367" y="745" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tạo task cho dev / de...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1497" y="701" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 741px; margin-left: 1498px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật trạng thái task của member
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1557" y="745" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Cập nhật trạng thái tas...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 736 L 230.63 736" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 235.88 736 L 228.88 739.5 L 230.63 736 L 228.88 732.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 77 310 L 77 270 L 1651 270 L 1651 310" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 310 L 77 450 L 1651 450 L 1651 310" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 310 L 1651 310" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 290px; margin-left: 864px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    Ban CNTT
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="864" y="294" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        Ban CNTT
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 360 L 230.63 360" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 235.88 360 L 228.88 363.5 L 230.63 360 L 228.88 356.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="360" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 360px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Khởi Tạo Dự Án
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Khởi Tạo Dự Án
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 357 360 L 430.63 360" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 435.88 360 L 428.88 363.5 L 430.63 360 L 428.88 356.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="237" y="320" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 360px; margin-left: 238px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Thêm bản ghi trên lark Project Management
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="297" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Thêm bản ghi trên lar...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1102 360 L 1155.63 360" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1160.88 360 L 1153.88 363.5 L 1155.63 360 L 1153.88 356.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="982" y="320" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 360px; margin-left: 983px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo Github repos
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1042" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tạo Github repos
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 742 360 L 795.63 360" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 800.88 360 L 793.88 363.5 L 795.63 360 L 793.88 356.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="622" y="320" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 360px; margin-left: 623px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo larkdrive chứa tài liệu dự án
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="682" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tạo larkdrive chứa tài...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 922 360 L 975.63 360" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 980.88 360 L 973.88 363.5 L 975.63 360 L 973.88 356.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="802" y="320" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 360px; margin-left: 803px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo group chat, link tới larkbase / larkdrive / github repos
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="862" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tạo group chat, link...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1162" y="330" width="100" height="60" rx="9" ry="9" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 360px; margin-left: 1163px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Bàn giao thông tin cho dự án
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1212" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Bàn giao thông t...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 557 360 L 615.63 360" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 620.88 360 L 613.88 363.5 L 615.63 360 L 613.88 356.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="437" y="320" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 360px; margin-left: 438px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo larkbase cho dự án từ template
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="497" y="364" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tạo larkbase cho dự án...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1212 390 L 1212 420 Q 1212 430 1202 430 L 157 430 Q 147 430 147 440 L 147 699.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 147 704.88 L 143.5 697.88 L 147 699.63 L 150.5 697.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="247" y="130" width="100" height="90" rx="13.5" ry="13.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 175px; margin-left: 248px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Khai báo các thông tin ban đầu (mã dự án, thành viên dự án, git repos...)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="297" y="179" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Khai báo các thô...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 297 320 L 297 226.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 297 221.12 L 300.5 228.12 L 297 226.37 L 293.5 228.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="622" y="130" width="120" height="90" rx="13.5" ry="13.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 175px; margin-left: 623px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tài liệu khách hàng, tài liệu BA, tài liệu test, và các tài liệu khác...
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="682" y="179" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tài liệu khách hàng,...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 682 320 L 682 226.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 682 221.12 L 685.5 228.12 L 682 226.37 L 678.5 228.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="787" y="90" width="150" height="120" rx="18" ry="18" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 150px; margin-left: 788px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Group trao đổi chính, đồng thời cũng là group nhận thông báo khi có sự kiện trong dự án
                                    <br/>
                                    <br/>
                                    (đang cân nhắc tách group nhận event notification sang group riêng)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="862" y="154" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Group trao đổi chính, đồng th...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 862 320 L 862 216.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 862 211.12 L 865.5 218.12 L 862 216.37 L 858.5 218.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="967" y="90" width="150" height="120" rx="18" ry="18" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 148px; height: 1px; padding-top: 150px; margin-left: 969px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - Quản lý source code
                                    <br/>
                                    - CI/CD
                                    <br/>
                                    - quản lý công việc của dev
                                    <br/>
                                    - Giao task cho dev ảo
                                    <br/>
                                    - có thể được đồng bộ với larkbase task cho PM
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="969" y="154" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Quản lý source code...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1042 320 L 1042 216.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1042 211.12 L 1045.5 218.12 L 1042 216.37 L 1038.5 218.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 77 490 L 77 450 L 1651 450 L 1651 490" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 490 L 77 630 L 1651 630 L 1651 490" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 490 L 1651 490" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <rect x="442" y="500" width="110" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 108px; height: 1px; padding-top: 555px; margin-left: 444px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - Đồng bộ về task cá nhân của từng người
                                    <br/>
                                    - Nếu là task implement, đồng bộ sang github issues
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="444" y="559" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Đồng bộ về task cá n...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1102" y="500" width="110" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 108px; height: 1px; padding-top: 555px; margin-left: 1104px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - Có thể được tạo tự động và assign tự động
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1104" y="559" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Có thể được tạo tự động...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 497 696 L 497 616.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 497 611.12 L 500.5 618.12 L 497 616.37 L 493.5 618.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="432" y="80" width="130" height="140" rx="19.5" ry="19.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 150px; margin-left: 434px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - Dashboard
                                    <br/>
                                    - Danh sách công việc
                                    <br/>
                                    - Danh sách bug (cho tester)
                                    <br/>
                                    - Danh sách phản hồi của khách hàng:
                                    <br/>
                                    +/ Thông báo lỗi từ hệ thống
                                    <div>
                                        +/ Liên hệ
                                        <br/>
                                        +/ Góp ý
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="434" y="154" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Dashboard...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 497 320 L 497 226.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 497 221.12 L 500.5 228.12 L 497 226.37 L 493.5 228.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 77 1046 L 77 1006 L 1651 1006 L 1651 1046" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 1046 L 77 1330 L 1651 1330 L 1651 1046" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 1046 L 1651 1046" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1026px; margin-left: 864px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    Phân tích nghiệp vụ (BA)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="864" y="1030" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        Phân tích nghiệp vụ (BA)
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 1096 L 280.63 1096" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 285.88 1096 L 278.88 1099.5 L 280.63 1096 L 278.88 1092.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="1096" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 1096px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="1100" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check task cá nh...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 407 1096 L 490.63 1096" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 495.88 1096 L 488.88 1099.5 L 490.63 1096 L 488.88 1092.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="287" y="1056" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1096px; margin-left: 289px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sử dụng custom Agent trên ChatGPT để:
                                    <br/>
                                    - tạo tài liệu nghiệp vụ (New Req, CR)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="289" y="1100" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sử dụng custom Agent t...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 617 1096 L 690.63 1096" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 695.88 1096 L 688.88 1099.5 L 690.63 1096 L 688.88 1092.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 497 1136 L 497 1150 Q 497 1160 487 1160 L 157 1160 Q 147 1160 147 1170 L 147 1183.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 147 1188.88 L 143.5 1181.88 L 147 1183.63 L 150.5 1181.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="497" y="1056" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 1096px; margin-left: 498px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Export kết quả phân tích, upload lên LarkDrive
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="557" y="1100" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Export kết quả phân tí...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="697" y="1056" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 1096px; margin-left: 698px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật trạng thái task ở trang task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="757" y="1100" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Cập nhật trạng thái tas...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 417 1240 L 490.63 1240" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 495.88 1240 L 488.88 1243.5 L 490.63 1240 L 488.88 1236.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="287" y="1167" width="130" height="146" rx="19.5" ry="19.5" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 1240px; margin-left: 289px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Khi có bất kỳ tài liệu mới:
                                    <br/>
                                    - Tài liệu nghiệp vụ mới phân tích
                                    <br/>
                                    - Tài liệu khách hàng gửi
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="289" y="1244" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Khi có bất kỳ tài liệu m...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 1220 L 280.78 1238.62" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 285.91 1239.76 L 278.32 1241.66 L 280.78 1238.62 L 279.83 1234.82 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="1220" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 1220px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Khi có tài liệu BA mới
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="1224" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Khi có tài liệu B...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="497" y="1167" width="130" height="146" rx="19.5" ry="19.5" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 1240px; margin-left: 499px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Upload tài liệu lên Google NotebookLM
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="499" y="1244" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Upload tài liệu lên Go...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="867" y="1160" width="130" height="146" rx="19.5" ry="19.5" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 128px; height: 1px; padding-top: 1233px; margin-left: 869px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Hỏi đáp trên notebooklm để hiểu các tài liệu đang có
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="869" y="1237" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Hỏi đáp trên notebooklm...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 627 1240 L 862.58 1240.01" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 867.83 1240.01 L 860.83 1243.51 L 862.58 1240.01 L 860.83 1236.51 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 867 1220.11 L 414.21 1130.28" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 409.06 1129.26 L 416.6 1127.19 L 414.21 1130.28 L 415.24 1134.05 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1191px; margin-left: 742px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Hỗ trợ tạo tài liệu phân tích nghiệp vụ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="742" y="1194" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        Hỗ trợ tạo tài liệu phân tích nghiệp vụ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 1290 L 281.43 1243.09" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 286.02 1240.54 L 281.6 1247 L 281.43 1243.09 L 278.2 1240.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="1290" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 1290px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tài liệu mới từ khách hàng
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="1294" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tài liệu mới từ khá...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 77 866 L 77 826 L 1651 826 L 1651 866" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 866 L 77 1006 L 1651 1006 L 1651 866" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 866 L 1651 866" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <rect x="291" y="880" width="110" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 108px; height: 1px; padding-top: 935px; margin-left: 293px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - Attach các tài liệu đang có của khách hàng
                                    <br/>
                                    - Attach các requirement đã có sẵn
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="293" y="939" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Attach các tài l...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="496" y="870" width="120" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 925px; margin-left: 498px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - File phân tích định dạng markdown
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="498" y="929" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - File phân tích định...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="697" y="870" width="120" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 925px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - Đồng bộ về larkbase task
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="699" y="929" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Đồng bộ về larkbase ta...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 347 1056 L 346.1 996.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 346.02 991.12 L 349.62 998.06 L 346.1 996.37 L 342.62 998.17 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 557 1056 L 556.08 986.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 556.01 981.12 L 559.61 988.07 L 556.08 986.37 L 552.61 988.16 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 757 1056 L 757 986.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 757 981.12 L 760.5 988.12 L 757 986.37 L 753.5 988.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 75 1550 L 75 1510 L 1649 1510 L 1649 1550" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 1550 L 75 2220 L 1649 2220 L 1649 1550" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 1550 L 1649 1550" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 1530px; margin-left: 862px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    SA / Techlead
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="862" y="1534" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        SA / Techlead
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 1610 L 274.63 1610" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 279.88 1610 L 272.88 1613.5 L 274.63 1610 L 272.88 1606.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="1610" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 1610px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="1614" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check task cá nh...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="281" y="1570" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1610px; margin-left: 283px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Phân loại task
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="283" y="1614" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Phân loại task
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 617 1610 L 690.63 1610" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 695.88 1610 L 688.88 1613.5 L 690.63 1610 L 688.88 1606.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 1645 L 547 1683.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 547 1688.88 L 543.5 1681.88 L 547 1683.63 L 550.5 1681.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 1575 L 617 1610 L 547 1645 L 477 1610 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 1610px; margin-left: 478px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Khởi tạo dự án
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="547" y="1614" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Khởi tạo dự án
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="697" y="1570" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1610px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - Khởi tạo code
                                    <br/>
                                    - System architecture
                                    <br/>
                                    - Deployment rules
                                    <br/>
                                    - Coding convention
                                    <br/>
                                    - ...
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="699" y="1614" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Khởi tạo code...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 401 1609.59 L 479.59 1609.06" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 484.84 1609.03 L 477.87 1612.57 L 479.59 1609.06 L 477.82 1605.57 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 617 1725 L 690.63 1725" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 695.88 1725 L 688.88 1728.5 L 690.63 1725 L 688.88 1721.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 1690 L 617 1725 L 547 1760 L 477 1725 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 1725px; margin-left: 478px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Có tài liệu BA mới
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="547" y="1729" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Có tài liệu BA mới
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 617 1935 L 690.63 1935" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 695.88 1935 L 688.88 1938.5 L 690.63 1935 L 688.88 1931.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 1970 L 547 1993.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 547 1998.88 L 543.5 1991.88 L 547 1993.63 L 550.5 1991.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 1900 L 617 1935 L 547 1970 L 477 1935 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 1935px; margin-left: 478px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Task implement feature, fix bug
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="547" y="1939" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Task implement feature,...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 817 1725 L 890.63 1725" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 895.88 1725 L 888.88 1728.5 L 890.63 1725 L 888.88 1721.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="697" y="1685" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1725px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Đưa tài liệu sang Github Repos (thủ công)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="699" y="1729" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Đưa tài liệu sang Githu...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1017 1725 L 1090.63 1725" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1095.88 1725 L 1088.88 1728.5 L 1090.63 1725 L 1088.88 1721.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="897" y="1685" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1725px; margin-left: 899px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Kích hoạt Github CI/CD đánh giá thay đổi
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="899" y="1729" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Kích hoạt Github CI/C...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1217 1725 L 1270.63 1725" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1275.88 1725 L 1268.88 1728.5 L 1270.63 1725 L 1268.88 1721.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1097" y="1685" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1725px; margin-left: 1099px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Review đánh giá
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1099" y="1729" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Review đánh giá
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1277" y="1650" width="120" height="150" rx="18" ry="18" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1725px; margin-left: 1279px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo github issue:
                                    <br/>
                                    - Assign cho designer nếu cần thiết kế
                                    <br/>
                                    - Assign cho dev nếu cần implement
                                    <br/>
                                    - Assign cho chính mình nếu cần làm tài liệu design hệ thống
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1279" y="1729" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Tạo github issue:...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="697" y="1895" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1935px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Thực hiện quy trình như dev
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="699" y="1939" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Thực hiện quy trình nh...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="697" y="1790" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1830px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo github issue để tracking
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="699" y="1834" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Tạo github issue để tra...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 817 2035 L 890.63 2035" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 895.88 2035 L 888.88 2038.5 L 890.63 2035 L 888.88 2031.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="697" y="1995" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2035px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Review code
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="699" y="2039" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Review code
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 617 2035 L 690.63 2035" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 695.88 2035 L 688.88 2038.5 L 690.63 2035 L 688.88 2031.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 2070 L 547 2103.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 547 2108.88 L 543.5 2101.88 L 547 2103.63 L 550.5 2101.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 2000 L 617 2035 L 547 2070 L 477 2035 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 2035px; margin-left: 478px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Review code
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="547" y="2039" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Review code
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1017 2035 L 1090.63 2035" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1095.88 2035 L 1088.88 2038.5 L 1090.63 2035 L 1088.88 2031.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="897" y="1995" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2035px; margin-left: 899px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Approve PR / Merge code
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="899" y="2039" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Approve PR / Merge c...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1097" y="1995" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2035px; margin-left: 1099px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Catch event cập nhật task review code
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1099" y="2039" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Catch event cập nhật t...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 177.4 1691.18 L 275.99 1613.93" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 280.12 1610.69 L 276.77 1617.76 L 275.99 1613.93 L 272.45 1612.25 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="1715" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 1715px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check github issues
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="1719" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check github iss...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 613.82 2146.59 L 690.63 2148.42" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 695.88 2148.54 L 688.8 2151.88 L 690.63 2148.42 L 688.97 2144.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 2110 L 617 2145 L 547 2180 L 477 2145 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 2145px; margin-left: 478px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Review Bug
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="547" y="2149" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Review Bug
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 387 2145 L 470.63 2145" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 475.88 2145 L 468.88 2148.5 L 470.63 2145 L 468.88 2141.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="267" y="2105" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2145px; margin-left: 269px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    CI/CD catch github bug được tạo, assign cho AI tool đánh giá bug và đưa ra gợi ý fix bug
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="269" y="2149" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        CI/CD catch github b...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 817 2150 L 890.63 2150" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 895.88 2150 L 888.88 2153.5 L 890.63 2150 L 888.88 2146.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="697" y="2110" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2150px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Review bug dựa trên thông tin Test / AI tool cung cấp
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="699" y="2154" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Review bug dựa trên t...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="897" y="2110" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2150px; margin-left: 899px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Assign cho người phu trách fix bug (Dev / Designer)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="899" y="2154" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Assign cho người phu t...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 75 1370 L 75 1330 L 1649 1330 L 1649 1370" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 1370 L 75 1510 L 1649 1510 L 1649 1370" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 1370 L 1649 1370" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <rect x="697" y="1380" width="120" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1435px; margin-left: 699px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    - Có thể kế thừa từ dự án khác hoặc kho template chung
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="699" y="1439" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Có thể kế thừa từ dự án...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1277" y="1380" width="120" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1435px; margin-left: 1279px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Đồng bộ github issue về larkbase task và task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1279" y="1439" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Đồng bộ github issue về...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="897" y="1380" width="120" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 1435px; margin-left: 899px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sử dụng AI Agent để phân tích nội dung thay đổi và đưa ra đánh giá
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="899" y="1439" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sử dụng AI Agent để phân...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 817 1096 L 847 1096 Q 857 1096 857 1086 L 857 751 Q 857 741 867 741 L 890.63 741" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 895.88 741 L 888.88 744.5 L 890.63 741 L 888.88 737.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 757 1570 L 757 1496.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 757 1491.12 L 760.5 1498.12 L 757 1496.37 L 753.5 1498.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 1860 L 547 1893.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 547 1898.88 L 543.5 1891.88 L 547 1893.63 L 550.5 1891.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 612.63 1827.19 L 690.64 1829.79" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 695.88 1829.96 L 688.77 1833.23 L 690.64 1829.79 L 689 1826.23 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 547 1790 L 617 1825 L 547 1860 L 477 1825 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 1825px; margin-left: 478px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Có issue cần hỗ trợ
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="547" y="1829" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Có issue cần hỗ trợ
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 547 1760 L 547 1783.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 547 1788.88 L 543.5 1781.88 L 547 1783.63 L 550.5 1781.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1157 701 L 1157 616.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1157 611.12 L 1160.5 618.12 L 1157 616.37 L 1153.5 618.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1157 781 L 1157 790.5 Q 1157 800 1147 800 L 57 800 Q 47 800 47 810 L 47 1600 Q 47 1610 57 1610 L 90.63 1610" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 95.88 1610 L 88.88 1613.5 L 90.63 1610 L 88.88 1606.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1337 1650 L 1337 1496.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1337 1491.12 L 1340.5 1498.12 L 1337 1496.37 L 1333.5 1498.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1397 1725 L 1427 1725 Q 1437 1725 1437 1715 L 1437 751 Q 1437 741 1435.18 741 L 1433.37 741" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1428.12 741 L 1435.12 737.5 L 1433.37 741 L 1435.12 744.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 817 1830 L 1427 1830 Q 1437 1830 1437 1820 L 1437 751 Q 1437 741 1435.18 741 L 1433.37 741" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1428.12 741 L 1435.12 737.5 L 1433.37 741 L 1435.12 744.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 957 1685 L 957 1496.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 957 1491.12 L 960.5 1498.12 L 957 1496.37 L 953.5 1498.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 77 2440 L 77 2400 L 1651 2400 L 1651 2440" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 2440 L 77 2720 L 1651 2720 L 1651 2440" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 2440 L 1651 2440" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 2420px; margin-left: 864px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    Designer
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="864" y="2424" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        Designer
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 2500 L 260.63 2500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 265.88 2500 L 258.88 2503.5 L 260.63 2500 L 258.88 2496.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="2500" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 2500px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="2504" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check task cá nh...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 2610 L 263.09 2525.03" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 266.31 2520.88 L 264.78 2528.56 L 263.09 2525.03 L 259.25 2524.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="2610" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 2610px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check github issue
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="2614" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check github iss...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 387 2500 L 470.63 2500" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 475.88 2500 L 468.88 2503.5 L 470.63 2500 L 468.88 2496.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 387 2500 L 473.88 2654.45" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 476.45 2659.03 L 469.97 2654.64 L 473.88 2654.45 L 476.07 2651.21 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="267" y="2460" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2500px; margin-left: 269px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sử dụng Figma để tạo / cập nhật design
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="269" y="2504" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sử dụng Figma để tạo / cập...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="477" y="2460" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2500px; margin-left: 479px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật github issue
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="479" y="2504" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Cập nhật github issue
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 597 2640 L 1547.5 2640 Q 1557.5 2640 1557.5 2630 L 1557 1016.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1557 1011.12 L 1560.5 1018.12 L 1557 1016.37 L 1553.5 1018.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="477" y="2600" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2640px; margin-left: 479px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="479" y="2644" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Cập nhật task cá nhân
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 77 2260 L 77 2220 L 1651 2220 L 1651 2260" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 2260 L 77 2400 L 1651 2400 L 1651 2260" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 77 2260 L 1651 2260" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <rect x="267" y="2270" width="120" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2325px; margin-left: 269px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sử dụng Figma AI hỗ trợ tạo nhanh các design
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="269" y="2329" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sử dụng Figma AI hỗ trợ...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="477" y="2270" width="120" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2325px; margin-left: 479px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tự động đồng bộ github issue, larkbase task và task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="479" y="2329" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Tự động đồng bộ github iss...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 327 2460 L 327 2386.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 327 2381.12 L 330.5 2388.12 L 327 2386.37 L 323.5 2388.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 537 2460 L 537 2386.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 537 2381.12 L 540.5 2388.12 L 537 2386.37 L 533.5 2388.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 477 2640 L 472 2640 Q 467 2640 467 2630 L 467 2335 Q 467 2325 468.82 2325 L 470.63 2325" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 475.88 2325 L 468.88 2328.5 L 470.63 2325 L 468.88 2321.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 597 2500 L 1547 2500 Q 1557 2500 1557 2490 L 1557 787.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1557 782.12 L 1560.5 789.12 L 1557 787.37 L 1553.5 789.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 75 2940 L 75 2900 L 1649 2900 L 1649 2940" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 2940 L 75 3165 L 1649 3165 L 1649 2940" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 2940 L 1649 2940" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 2920px; margin-left: 862px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    Dev
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="862" y="2924" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        Dev
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 2980 L 260.7 2989.1" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 265.89 2989.84 L 258.47 2992.32 L 260.7 2989.1 L 259.46 2985.39 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="2980" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 2980px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="2984" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check task cá nh...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 197 3090 L 262.81 3014.79" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 266.26 3010.84 L 264.29 3018.41 L 262.81 3014.79 L 259.02 3013.8 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="147" cy="3090" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 3090px; margin-left: 98px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check github issue (task / bug)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="147" y="3094" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check github iss...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 387 2990 L 470.63 2990" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 475.88 2990 L 468.88 2993.5 L 470.63 2990 L 468.88 2986.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="267" y="2950" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2990px; margin-left: 269px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sử dụng IDE + AI tool để implement
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="269" y="2994" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sử dụng IDE + AI tool...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 597 2990 L 670.63 2990" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 675.88 2990 L 668.88 2993.5 L 670.63 2990 L 668.88 2986.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="477" y="2950" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2990px; margin-left: 479px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo PR trên github
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="479" y="2994" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Tạo PR trên github
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 737 3030 L 737 3060 Q 737 3070 727 3070 L 337 3070 Q 327 3070 327 3060 L 327 3036.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 327 3031.12 L 330.5 3038.12 L 327 3036.37 L 323.5 3038.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 3072px; margin-left: 568px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Fix code review
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="568" y="3075" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        Fix code review
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 797 2990 L 857.63 2990" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 862.88 2990 L 855.88 2993.5 L 857.63 2990 L 855.88 2986.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="677" y="2950" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2990px; margin-left: 679px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Trigger CI/CD:
                                    <br/>
                                    - Review code tự động
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="679" y="2994" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Trigger CI/CD:...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1130.98 3024.02 L 1131 3115 Q 1131 3125 1121 3125 L 337 3125 Q 327 3125 327 3115 L 327 3036.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 327 3031.12 L 330.5 3038.12 L 327 3036.37 L 323.5 3038.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 3124px; margin-left: 707px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Fix code review
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="707" y="3127" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        Fix code review
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 984 2990 L 1060.63 2990" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1065.88 2990 L 1058.88 2993.5 L 1060.63 2990 L 1058.88 2986.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="864" y="2950" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2990px; margin-left: 866px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Chọn người review code
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="866" y="2994" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Chọn người review code
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1207 2990 L 1280.63 2990" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1285.88 2990 L 1278.88 2993.5 L 1280.63 2990 L 1278.88 2986.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1207 2990 L 1283.75 3119.52" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1286.43 3124.04 L 1279.85 3119.8 L 1283.75 3119.52 L 1285.87 3116.23 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1137 2955 L 1207 2990 L 1137 3025 L 1067 2990 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 2990px; margin-left: 1068px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Nếu có comment review
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1137" y="2994" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Nếu có comment review
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1287" y="2950" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2990px; margin-left: 1289px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Đánh dấu Github Issue hoàn thành
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1289" y="2994" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Đánh dấu Github Issue...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1407 3105 L 1547.5 3105 Q 1557.5 3105 1557.5 3095 L 1557 911.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1557 906.12 L 1560.5 913.12 L 1557 911.37 L 1553.5 913.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1287" y="3065" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3105px; margin-left: 1289px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1289" y="3109" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Cập nhật task cá nhân
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 75 2760 L 75 2720 L 1649 2720 L 1649 2760" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 2760 L 75 2900 L 1649 2900 L 1649 2760" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 2760 L 1649 2760" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <rect x="237" y="2770" width="190" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 2825px; margin-left: 239px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sử dụng:
                                    <br/>
                                    - VSCode + Github Copilot / Augment / Claude Code / Gemini CLI
                                    <br/>
                                    - Sử dụng Intelliji + Github Copilot / Augment Code
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="239" y="2829" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sử dụng:...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 506.55 2885 L 430.85 2984.92" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 427.68 2989.11 L 429.11 2981.42 L 430.85 2984.92 L 434.69 2985.64 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="467" y="2765" width="170" height="120" rx="18" ry="18" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 2825px; margin-left: 469px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sử dụng Github Copilot để:
                                    <br/>
                                    - Review code theo file convention
                                    <br/>
                                    - Review code theo tài liệu BA
                                    <br/>
                                    - Tạo commit message tự động
                                    <div>
                                        <br/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="469" y="2829" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sử dụng Github Copilot để:...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="677" y="2765" width="170" height="120" rx="18" ry="18" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 168px; height: 1px; padding-top: 2825px; margin-left: 679px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <div>
                                        - Github copilot
                                        <br/>
                                        - Code Rabbit
                                    </div>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="679" y="2829" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        - Github copilot...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="87" y="2770" width="138" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 136px; height: 1px; padding-top: 2825px; margin-left: 89px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Input:
                                    <br/>
                                    - Tài liệu BA
                                    <br/>
                                    - Figma design
                                    <br/>
                                    - Tài liệu design của SA / Techlead
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="89" y="2829" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Input:...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1277" y="2775" width="120" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2830px; margin-left: 1279px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    <span style="color: light-dark(rgb(0, 0, 0), rgb(237, 237, 237));">
                                        Tự động đồng bộ github issue, larkbase task và task cá nhân
                                    </span>
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1279" y="2834" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Tự động đồng bộ github iss...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 75 3385 L 75 3345 L 1649 3345 L 1649 3385" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 3385 L 75 3680 L 1649 3680 L 1649 3385" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 3385 L 1649 3385" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 3365px; margin-left: 862px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    Tester
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="862" y="3369" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        Tester
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 206 3435 L 250.63 3435" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 255.88 3435 L 248.88 3438.5 L 250.63 3435 L 248.88 3431.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 195.79 3453.16 L 391.21 3542.36" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 395.98 3544.54 L 388.16 3544.81 L 391.21 3542.36 L 391.07 3538.45 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="156" cy="3435" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 3435px; margin-left: 107px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="156" y="3439" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check task cá nh...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="257" y="3395" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3435px; margin-left: 259px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sử dụng ChatGPT Custom agent để tạo test case
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="259" y="3439" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sử dụng ChatGPT Custom...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 517 3545 L 651.47 3468.16" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 656.03 3465.55 L 651.69 3472.07 L 651.47 3468.16 L 648.22 3465.99 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="397" y="3505" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3545px; margin-left: 399px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    (Đang nghiên cứu)
                                    <br/>
                                    Sử dụng các AI tools khác hỗ trợ tạo test case
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="399" y="3549" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        (Đang nghiên cứu)...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 777 3445 L 870.63 3445" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 875.88 3445 L 868.88 3448.5 L 870.63 3445 L 868.88 3441.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="657" y="3405" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3445px; margin-left: 659px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật test case lên tài liệu trên larkdrive
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="659" y="3449" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Cập nhật test case lên...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 377 3435 L 647.63 3438.91" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 652.88 3438.98 L 645.83 3442.38 L 647.63 3438.91 L 645.93 3435.38 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 997 3445 L 1060.63 3445" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1065.88 3445 L 1058.88 3448.5 L 1060.63 3445 L 1058.88 3441.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="877" y="3405" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3445px; margin-left: 879px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Thực hiện test
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="879" y="3449" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Thực hiện test
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1207 3445 L 1260.63 3445" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1265.88 3445 L 1258.88 3448.5 L 1260.63 3445 L 1258.88 3441.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 3448px; margin-left: 1240px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Có
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1240" y="3451" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        Có
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1137 3480 L 1137 3595 Q 1137 3605 1147 3605 L 1260.63 3605" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1265.88 3605 L 1258.88 3608.5 L 1260.63 3605 L 1258.88 3601.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 3607px; margin-left: 1176px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; background-color: #ffffff; ">
                                <div style="display: inline-block; font-size: 11px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; background-color: light-dark(#ffffff, var(--ge-dark-color, #121212)); white-space: nowrap; ">
                                    Không
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1176" y="3610" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="11px" text-anchor="middle">
                        Không
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1137 3410 L 1207 3445 L 1137 3480 L 1067 3445 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 3445px; margin-left: 1068px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Có bug?
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1137" y="3449" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Có bug?
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1267" y="3405" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3445px; margin-left: 1269px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo bug trên larkbase, assign cho techlead / SA của dự án
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1269" y="3449" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Tạo bug trên larkbase...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1267" y="3565" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3605px; margin-left: 1269px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật trạng thái task test cho chức năng
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1269" y="3609" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Cập nhật trạng thái tas...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 75 3205 L 75 3165 L 1649 3165 L 1649 3205" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 3205 L 75 3345 L 1649 3345 L 1649 3205" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 3205 L 1649 3205" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <rect x="222" y="3215" width="190" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 3270px; margin-left: 224px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Đính kèm các tài liệu liên quan và chatGPT agent sẽ trả về các test case
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="224" y="3274" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Đính kèm các tài liệu liên quan v...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="487" y="3215" width="190" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 3270px; margin-left: 489px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Nghiên cứu các tools:
                                    <br/>
                                    - Katalon Studio
                                    <br/>
                                    - Test Rigor
                                    <br/>
                                    - ...Các tool khác tương tự
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="489" y="3274" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Nghiên cứu các tools:...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="837" y="3215" width="190" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 3270px; margin-left: 839px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Sẽ có thông báo từ dev khi nào implement xong tính năng để bắt đầu test
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="839" y="3274" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Sẽ có thông báo từ dev khi nào im...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1232" y="3215" width="190" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 3270px; margin-left: 1234px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Đồng bộ bug trên larkbase sang Github issues và assign cho techlead / SA
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1234" y="3274" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Đồng bộ bug trên larkbase sang Git...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 75 3901 L 75 3861 L 1649 3861 L 1649 3901" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 3901 L 75 4341 L 1649 4341 L 1649 3901" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 3901 L 1649 3901" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 3881px; margin-left: 862px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 14px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; font-weight: bold; white-space: nowrap; ">
                                    DevOps
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="862" y="3885" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="14px" text-anchor="middle" font-weight="bold">
                        DevOps
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 216 3950 L 270.63 3950" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 275.88 3950 L 268.88 3953.5 L 270.63 3950 L 268.88 3946.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <ellipse cx="166" cy="3950" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 3950px; margin-left: 117px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check task cá nhân
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="166" y="3954" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check task cá nh...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <ellipse cx="166" cy="4060" rx="50" ry="30" fill="#e1d5e7" stroke="#9673a6" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 98px; height: 1px; padding-top: 4060px; margin-left: 117px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Check github issue
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="166" y="4064" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Check github iss...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 397 3950 L 450.65 3945.53" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 455.89 3945.09 L 449.2 3949.16 L 450.65 3945.53 L 448.62 3942.19 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="277" y="3910" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3950px; margin-left: 279px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Phân loại task
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="279" y="3954" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Phân loại task
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 216 4060 L 271.77 3963.51" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 274.4 3958.97 L 273.93 3966.78 L 271.77 3963.51 L 267.87 3963.28 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 527 3980 L 527 4023.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 527 4028.88 L 523.5 4021.88 L 527 4023.63 L 530.5 4021.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 597 3945 L 650.65 3949.47" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 655.89 3949.91 L 648.62 3952.81 L 650.65 3949.47 L 649.2 3945.84 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 527 3910 L 597 3945 L 527 3980 L 457 3945 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 3945px; margin-left: 458px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    khởi tạo môi trường dev / test / stagging
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="527" y="3949" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        khởi tạo môi trường dev / t...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 527 4100 L 527 4143.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 527 4148.88 L 523.5 4141.88 L 527 4143.63 L 530.5 4141.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 597 4065 L 650.65 4069.47" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 655.89 4069.91 L 648.62 4072.81 L 650.65 4069.47 L 649.2 4065.84 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 527 4030 L 597 4065 L 527 4100 L 457 4065 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 4065px; margin-left: 458px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo CI/CD
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="527" y="4069" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Tạo CI/CD
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 527 4220 L 527 4253.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 527 4258.88 L 523.5 4251.88 L 527 4253.63 L 530.5 4251.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 597 4185 L 650.65 4189.47" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 655.89 4189.91 L 648.62 4192.81 L 650.65 4189.47 L 649.2 4185.84 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 527 4150 L 597 4185 L 527 4220 L 457 4185 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 4185px; margin-left: 458px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Triển khai Dev / Test
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="527" y="4189" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Triển khai Dev / Test
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 592 4292.5 L 650.64 4290.24" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 655.88 4290.04 L 649.02 4293.81 L 650.64 4290.24 L 648.75 4286.81 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 527 4260 L 597 4295 L 527 4330 L 457 4295 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 4295px; margin-left: 458px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Triển khai Stagging / Production
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="527" y="4299" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Triển khai Stagging / Pr...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 777 3950 L 981.25 4047.26" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 985.99 4049.52 L 978.17 4049.67 L 981.25 4047.26 L 981.18 4043.35 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="657" y="3910" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 3950px; margin-left: 659px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Khởi tạo môi trường triển khai trên máy chủ (Phần lớn sử dụng K8s)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="659" y="3954" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Khởi tạo môi trường triển...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 777 4070 L 980.63 4070" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 985.88 4070 L 978.88 4073.5 L 980.63 4070 L 978.88 4066.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="657" y="4030" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 4070px; margin-left: 659px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Khởi tạo CI/CD
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="659" y="4074" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Khởi tạo CI/CD
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 777 4190 L 981.25 4092.74" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 985.99 4090.48 L 981.18 4096.65 L 981.25 4092.74 L 978.17 4090.33 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="657" y="4150" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 4190px; margin-left: 659px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Triển khai theo yêu cầu thông qua trigger pipeline
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="659" y="4194" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Triển khai theo yêu c...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 777 4290 L 982.17 4114.14" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 986.15 4110.73 L 983.11 4117.94 L 982.17 4114.14 L 978.56 4112.63 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="657" y="4250" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 4290px; margin-left: 659px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Triển khai theo yêu cầu thông qua trigger pipeline (Có approve từ PM / SA / Techlead)
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="659" y="4294" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Triển khai theo yêu c...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="987" y="4030" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 4070px; margin-left: 989px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Cập nhật trạng thái task
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="989" y="4074" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Cập nhật trạng thái task
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 75 3721 L 75 3681 L 1649 3681 L 1649 3721" fill="#e1d5e7" stroke="#9673a6" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(225, 213, 231), rgb(57, 47, 63)); stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 3721 L 75 3861 L 1649 3861 L 1649 3721" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
            <path d="M 75 3721 L 1649 3721" fill="none" stroke="#9673a6" stroke-miterlimit="10" pointer-events="none" style="stroke: light-dark(rgb(150, 115, 166), rgb(149, 119, 163));"/>
        </g>
        <g>
            <rect x="616" y="3730" width="190" height="110" rx="16.5" ry="16.5" fill="#dae8fc" stroke="#6c8ebf" pointer-events="all" style="fill: light-dark(rgb(218, 232, 252), rgb(29, 41, 59)); stroke: light-dark(rgb(108, 142, 191), rgb(92, 121, 163));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 188px; height: 1px; padding-top: 3785px; margin-left: 618px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Có thể kế thừa lại từ dự án khác hoặc sử dụng lại template chung của công ty
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="618" y="3789" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Có thể kế thừa lại từ dự án khác hoặc...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 327 2950 L 326.02 2885.38" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 325.94 2880.13 L 329.54 2887.07 L 326.02 2885.38 L 322.54 2887.18 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 746.52 2950 L 760.53 2891.19" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 761.74 2886.09 L 763.52 2893.71 L 760.53 2891.19 L 756.71 2892.09 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1217 2035 L 1547 2035 Q 1557 2035 1557 2025 L 1557 787.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1557 782.12 L 1560.5 789.12 L 1557 787.37 L 1553.5 789.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 915 2951.04 L 915 2810 Q 915 2800 925 2800 L 1427 2800 Q 1437 2800 1437 2790 L 1437 751 Q 1437 741 1435.18 741 L 1433.37 741" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1428.12 741 L 1435.12 737.5 L 1433.37 741 L 1435.12 744.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 218.04 2880 L 292.23 2945.78" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 296.16 2949.26 L 288.6 2947.23 L 292.23 2945.78 L 293.25 2942 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1407 2990 L 1547 2990 Q 1557 2990 1557 2980 L 1557 787.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1557 782.12 L 1560.5 789.12 L 1557 787.37 L 1553.5 789.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1347 2950 L 1345.24 2893.34" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1345.07 2888.1 L 1348.79 2894.99 L 1345.24 2893.34 L 1341.79 2895.2 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1287 3105 L 1277 3105 Q 1267 3105 1267 3095 L 1267 2840 Q 1267 2830 1268.82 2830 L 1270.63 2830" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1275.88 2830 L 1268.88 2833.5 L 1270.63 2830 L 1268.88 2826.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1767 2080 L 1767 2138.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1767 2143.88 L 1763.5 2136.88 L 1767 2138.63 L 1770.5 2136.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1707" y="2000" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2040px; margin-left: 1709px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Trigger CI/CD
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1709" y="2044" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Trigger CI/CD
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 957 1995 L 957 1950 Q 957 1940 967 1940 L 1757 1940 Q 1767 1940 1767 1950 L 1767 1993.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1767 1998.88 L 1763.5 1991.88 L 1767 1993.63 L 1770.5 1991.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1767 2225 L 1767 2293.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1767 2298.88 L 1763.5 2291.88 L 1767 2293.63 L 1770.5 2291.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1707" y="2145" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2185px; margin-left: 1709px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Ghi change log vào github repos
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1709" y="2189" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Ghi change log vào g...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1767 2380 L 1767 2423.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1767 2428.88 L 1763.5 2421.88 L 1767 2423.63 L 1770.5 2421.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1827 2340 L 1847 2340 Q 1857 2340 1857 2330 L 1857 2050 Q 1857 2040 1867 2040 L 1880.63 2040" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1885.88 2040 L 1878.88 2043.5 L 1880.63 2040 L 1878.88 2036.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1707" y="2300" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2340px; margin-left: 1709px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Gen UT
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1709" y="2344" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Gen UT
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <rect x="1707" y="2430" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2470px; margin-left: 1709px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Gen Automation test
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1709" y="2474" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Gen Automation test
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1947 2080 L 1947 2148.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1947 2153.88 L 1943.5 2146.88 L 1947 2148.63 L 1950.5 2146.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1887" y="2000" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2040px; margin-left: 1889px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    CI/CD schedule run test
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1889" y="2044" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        CI/CD schedule run t...
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1947 2225 L 1947 2293.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1947 2298.88 L 1943.5 2291.88 L 1947 2293.63 L 1950.5 2291.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1947 2155 L 2017 2190 L 1947 2225 L 1877 2190 Z" fill="#fff2cc" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(255, 242, 204), rgb(40, 29, 0)); stroke: light-dark(rgb(214, 182, 86), rgb(109, 81, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 138px; height: 1px; padding-top: 2190px; margin-left: 1878px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: center; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Có bug?
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1947" y="2194" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px" text-anchor="middle">
                        Có bug?
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 2007 2340 L 2017 2340 Q 2027 2340 2027 2330 L 2027 701 Q 2027 691 2017 691 L 1377 691 Q 1367 691 1367 692.82 L 1367 694.63" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1367 699.88 L 1363.5 692.88 L 1367 694.63 L 1370.5 692.88 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <rect x="1887" y="2300" width="120" height="80" rx="12" ry="12" fill="#ffe6cc" stroke="#d79b00" stroke-dasharray="3 3" pointer-events="all" style="fill: light-dark(rgb(255, 230, 204), rgb(54, 33, 10)); stroke: light-dark(rgb(215, 155, 0), rgb(153, 101, 0));"/>
        </g>
        <g>
            <g transform="translate(-0.5 -0.5)">
                <switch>
                    <foreignObject style="overflow: visible; text-align: left;" pointer-events="none" width="100%" height="100%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility">
                        <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe flex-start; width: 118px; height: 1px; padding-top: 2340px; margin-left: 1889px;">
                            <div style="box-sizing: border-box; font-size: 0; text-align: left; color: #000000; ">
                                <div style="display: inline-block; font-size: 12px; font-family: &quot;Helvetica&quot;; color: light-dark(#000000, #ffffff); line-height: 1.2; pointer-events: all; white-space: normal; word-wrap: normal; ">
                                    Tạo github issues
                                </div>
                            </div>
                        </div>
                    </foreignObject>
                    <text x="1889" y="2344" fill="light-dark(#000000, #ffffff)" font-family="&quot;Helvetica&quot;" font-size="12px">
                        Tạo github issues
                    </text>
                </switch>
            </g>
        </g>
        <g>
            <path d="M 1827 2470 L 1847 2470 Q 1857 2470 1857 2460 L 1857 2057 Q 1857 2047 1867 2047.01 L 1877.63 2047.03" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1882.88 2047.04 L 1875.88 2050.53 L 1877.63 2047.03 L 1875.89 2043.53 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1157 781 L 1157 800 Q 1157 810 1147 810 L 17 810 Q 7 810 7 820 L 7 3425 Q 7 3435 17 3435 L 99.63 3435" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 104.88 3435 L 97.88 3438.5 L 99.63 3435 L 97.88 3431.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 317 3395 L 317 3331.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 317 3326.12 L 320.5 3333.12 L 317 3331.37 L 313.5 3333.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 479.73 3505 L 578.85 3330.54" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 581.45 3325.97 L 581.03 3333.79 L 578.85 3330.54 L 574.95 3330.33 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 935.33 3405 L 932.27 3331.36" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 932.05 3326.12 L 935.83 3332.97 L 932.27 3331.36 L 928.84 3333.26 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1327 3405 L 1327 3331.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1327 3326.12 L 1330.5 3333.12 L 1327 3331.37 L 1323.5 3333.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1387 3605 L 1547 3605 Q 1557 3605 1557 3595 L 1557 787.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1557 782.12 L 1560.5 789.12 L 1557 787.37 L 1553.5 789.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1017 2150 L 1427 2150 Q 1437 2150 1437 2140 L 1437 751 Q 1437 741 1435.18 741 L 1433.37 741" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1428.12 741 L 1435.12 737.5 L 1433.37 741 L 1435.12 744.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 717 3910 L 711.54 3846.34" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 711.1 3841.11 L 715.18 3847.79 L 711.54 3846.34 L 708.21 3848.39 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 657 4050 L 617 4050 Q 607 4050 607 4040 L 607 3945 Q 607 3935 606.5 3935 L 606.25 3935 Q 606 3935 606 3925 L 606 3795 Q 606 3785 607.82 3785 L 609.63 3785" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 614.88 3785 L 607.88 3788.5 L 609.63 3785 L 607.88 3781.5 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
        <g>
            <path d="M 1107 4070 L 1547 4070 Q 1557 4070 1557 4060 L 1557 787.37" fill="none" stroke="#000000" stroke-miterlimit="10" pointer-events="stroke" style="stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
            <path d="M 1557 782.12 L 1560.5 789.12 L 1557 787.37 L 1553.5 789.12 Z" fill="#000000" stroke="#000000" stroke-miterlimit="10" pointer-events="all" style="fill: light-dark(rgb(0, 0, 0), rgb(255, 255, 255)); stroke: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));"/>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.drawio.com/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>